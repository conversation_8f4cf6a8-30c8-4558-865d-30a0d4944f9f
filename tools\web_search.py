import httpx
from config.settings import get_settings

settings = get_settings()

async def search_web(query: str):
    """Search the web using SerpAPI (async)."""
    url = "https://serpapi.com/search"
    params = {"q": query, "api_key": settings.serpapi_key}
    async with httpx.AsyncClient(timeout=10) as client:
        r = await client.get(url, params=params)
        r.raise_for_status()
        data = r.json()
        return [
            {
                "title": item.get("title"),
                "link": item.get("link"),
                "snippet": item.get("snippet"),
            }
            for item in data.get("organic_results", [])[:5]
        ]
